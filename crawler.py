import requests
import json
import time
from typing import List, Dict, Any

class CTFKaierCrawler:
    def __init__(self):
        self.base_url = "https://ctfkaier.edu.deal/api/keys"
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "authorization": "Bearer ctfkaier-2147483647",
            "cache-control": "no-cache",
            "connection": "keep-alive",
            "host": "ctfkaier.edu.deal",
            "pragma": "no-cache",
            "referer": "https://ctfkaier.edu.deal/keys?groupId=1",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def fetch_page(self, page: int, group_id: int = 1, page_size: int = 12) -> Dict[str, Any]:
        """获取指定页面的数据"""
        params = {
            "group_id": group_id,
            "page": page,
            "page_size": page_size
        }
        
        try:
            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求第{page}页时出错: {e}")
            return None
    
    def crawl_all_pages(self, start_page: int = 1, end_page: int = 57, group_id: int = 1, page_size: int = 12) -> List[Dict[str, Any]]:
        """爬取所有页面数据"""
        all_data = []
        
        print(f"开始爬取第{start_page}页到第{end_page}页的数据...")
        
        for page in range(start_page, end_page + 1):
            print(f"正在爬取第{page}页...")
            
            data = self.fetch_page(page, group_id, page_size)
            if data:
                all_data.append({
                    "page": page,
                    "data": data
                })
                print(f"第{page}页爬取成功")
            else:
                print(f"第{page}页爬取失败")
            
            # 添加延时避免请求过快
            time.sleep(0.5)
        
        print(f"爬取完成，共获取{len(all_data)}页数据")
        return all_data
    
    def save_to_file(self, data: List[Dict[str, Any]], filename: str = "crawled_data.json"):
        """保存数据到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存文件时出错: {e}")

def main():
    crawler = CTFKaierCrawler()
    
    # 爬取第1页到第57页
    all_data = crawler.crawl_all_pages(start_page=1, end_page=57)
    
    # 保存到文件
    crawler.save_to_file(all_data, "ctfkaier_keys_data.json")
    
    # 统计信息
    total_items = 0
    for page_data in all_data:
        if 'data' in page_data and isinstance(page_data['data'], dict):
            # 根据实际API响应结构调整
            if 'data' in page_data['data']:
                total_items += len(page_data['data']['data'])
    
    print(f"\n爬取统计:")
    print(f"总页数: {len(all_data)}")
    print(f"总条目数: {total_items}")

if __name__ == "__main__":
    main()